import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { Repository, ILike, DataSource, SelectQueryBuilder } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CreateAppDto,
  DeleteAppDto,
  FetchAppsDto,
  FetchSingleAppDto,
  UpdateAppDto,
} from './dto/application.dto';
import {
  CommonMessage,
  AppMessage,
  UserMessage,
} from '../CommonMessages/CommonMessages';
import {
  paginate,
  validateChainId,
  validateUrlFromDatabase,
} from '../utils/common.service';
import { GatewayService } from '../gateway/gateway.service';
import { User } from '../user/entities/user.entity';
import { Application } from './entities/application.entity';
import { EPVersions, Plans, SUPPORTED_ENTRYPOINTS } from '../utils/constants';
import {
  whitelistValidatorForRelayerV2,
  whitelistValidatorForRelayerV3,
  // whitelistValidatorV2,
} from '../utils/whitelist.service';
import { Bundler } from '../bundler/entities/bundler.entity';
import { OriginType } from '../origins/entities/origins.entity';
import { Paymaster } from '../paymaster/entities/paymaster.entity';
import { AppRelayer } from 'src/appRelayer/entities/appRelayer.entity';
import { ChatAi } from '../chatAi/entities/chatAi.entity';
import {
  handleValidateUserBalance,
  handleValidateUserBalanceV2,
} from 'src/utils/utils.ethers';

@Injectable()
export class ApplicationService {
  private readonly logger = new Logger(ApplicationService.name, {
    timestamp: true,
  });
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly gatewayService: GatewayService,
    private readonly dataSource: DataSource,
  ) {}

  async createApp(userId: number, createAppDto: CreateAppDto) {
    try {
      const chainName = await validateChainId(createAppDto.chainId);
      if (!chainName) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.InvalidChainId,
        };
      }
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.UserNotFound,
        };
      }
      if (!user.plan) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: UserMessage.NotAllowedToCreateApp,
        };
      }
      const appCount = await this.applicationRepository.count({
        where: {
          user: { id: userId },
        },
      });
      if (appCount >= Plans[user.plan].appsLimit) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppLimitExceed(Plans[user.plan].appsLimit),
        };
      }
      const isExist = await this.applicationRepository.findOne({
        where: {
          appName: ILike(`${createAppDto.appName}`),
          user: { id: userId },
        },
      });
      if (isExist) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadyCreated(),
        };
      }
      const apiKey = await this.gatewayService.assignConsumerkey({
        username: user.username,
      });
      if (apiKey.error) {
        return apiKey;
      }
      const newApp = this.applicationRepository.create({
        ...createAppDto,
        apiKey: apiKey?.result?.key,
        user: { id: userId },
      });
      await this.applicationRepository.save(newApp);

      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated(),
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateApp(userId: number, updateAppDto: UpdateAppDto) {
    try {
      let responseMessage = null;
      const app = await this.applicationRepository.findOne({
        where: { id: updateAppDto.id, user: { id: userId } },
        relations: {
          user: true,
        },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }

      if (
        (updateAppDto.appName && updateAppDto.appName === app.appName) ||
        (updateAppDto.appDescription &&
          updateAppDto.appDescription === app.appDescription)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SameValues,
        };
      }

      if (updateAppDto.appName !== undefined) {
        responseMessage = `App updated`;
        app.appName = updateAppDto.appName;
      }

      if (updateAppDto.appDescription !== undefined) {
        responseMessage = `App updated`;
        app.appDescription = updateAppDto.appDescription;
      }

      if (updateAppDto.isActive !== undefined) {
        if (app.isActive === updateAppDto.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive is'),
          };
        }
        let updateKey;
        switch (updateAppDto.isActive) {
          case true:
            updateKey = await this.gatewayService.assignConsumerkey({
              username: app.user.username,
              apiKey: app.apiKey,
            });
            if (updateKey.error) {
              return updateKey;
            }
            break;
          case false:
            updateKey = await this.gatewayService.deleteConsumerkey({
              username: app.user.username,
              apiKey: app.apiKey,
            });
            break;
          default:
            break;
        }
        responseMessage = `App ${updateAppDto.isActive ? 'active' : 'inactive'}`;
        app.isActive = updateAppDto.isActive;
      }

      await this.applicationRepository.save(app);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: responseMessage
          ? AppMessage.AppUpdated(responseMessage)
          : AppMessage.AppUpdated,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updateApp app:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getSingleApp(userId: number, query: FetchSingleAppDto) {
    try {
      const whereClause: any = { id: query.id, user: { id: userId } };
      const result: any = await this.applicationRepository.findOne({
        where: whereClause,
        relations: {
          bundler: true,
          paymaster: true,
          relayer: true,
        },
      });
      if (!result) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.NoDataFound,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: AppMessage.AppFetched,
        result: result,
      };
    } catch (error) {
      console.log('error', error);
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllApps(userId: number, query: FetchAppsDto) {
    try {
      let whereClause: any = { user: { id: userId } };
      const list: boolean = query.list;
      const type: string = query.type;
      if (list) {
        if (type == null) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.INVALID_SERVICE_TYPE,
          };
        }
        const queryBuilder = this.applicationRepository
          .createQueryBuilder('application')
          .innerJoin('application.user', 'user')
          .where('user.id = :userId', { userId })
          .orderBy('application.createdAt', 'DESC');

        if (type === OriginType.BUNDLER) {
          queryBuilder.leftJoinAndSelect('application.bundler', 'bundler');
        } else if (type === OriginType.PAYMASTER) {
          queryBuilder.leftJoinAndSelect('application.paymaster', 'paymaster');
        } else if (type === OriginType.RELAYER) {
          queryBuilder.leftJoinAndSelect('application.relayer', 'relayer');
        } else if (type === OriginType.CHATAI) {
          queryBuilder.leftJoinAndSelect('application.chatAi', 'chatAi');
        }

        const [result, totalCount] = await queryBuilder.getManyAndCount();
        return {
          error: false,
          statusCode: HttpStatus.ACCEPTED,
          message:
            totalCount > 0 ? AppMessage.AppsFetched : CommonMessage.NoDataFound,
          result: result,
        };
      }
      if (query.s) {
        whereClause = {
          ...whereClause,
          appName: ILike(`%${query.s}%`),
        };
      }
      if (query.chainId) {
        whereClause = {
          ...whereClause,
          chainId: query.chainId,
        };
      }
      const skip = paginate(query.page, query.limit);

      const [result, totalCount] =
        await this.applicationRepository.findAndCount({
          where: whereClause,
          take: query.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0 ? AppMessage.AppsFetched : CommonMessage.NoDataFound,
        result: result,
        totalCount: totalCount,
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async deleteApp(userId: number, query: DeleteAppDto) {
    try {
      const app = await this.applicationRepository.findOne({
        where: { id: query.id, user: { id: userId } },
        relations: {
          user: true,
        },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound(),
        };
      }
      await this.gatewayService.deleteConsumerkey({
        username: app.user.username,
        apiKey: app.apiKey,
      });
      await this.applicationRepository.remove(app);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.FeatureRemoved('Application'),
      };
    } catch (error) {
      this.logger.error('error in catch at deleteApp', JSON.stringify(error));
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async apiKeyValidator(
    chainId: number,
    apiKey: string,
    origin: string,
    payload: any,
    type: string,
  ) {
    try {
      let query: SelectQueryBuilder<
        any
      > = null;
      switch (type) {
        case OriginType.BUNDLER:
          query = this.dataSource
            .getRepository(Bundler)
            .createQueryBuilder('bundler')
            .innerJoinAndSelect('bundler.app', 'app', 'app.apiKey = :apiKey', {
              apiKey: apiKey,
            })
            .leftJoinAndSelect(
              'app.origins',
              'origins',
              'origins.type =:type',
              {
                type: type,
              },
            );
          break;
        case OriginType.PAYMASTER:
          query = this.dataSource
            .getRepository(Paymaster)
            .createQueryBuilder('paymaster')
            .innerJoinAndSelect(
              'paymaster.app',
              'app',
              'app.apiKey = :apiKey',
              {
                apiKey: apiKey,
              },
            )
            .leftJoinAndSelect(
              'app.origins',
              'origins',
              'origins.type =:type',
              {
                type: type,
              },
            )
            .leftJoinAndSelect('app.smartContracts', 'smartContracts');
          break;
        case OriginType.RELAYER:
          query = this.dataSource
            .getRepository(AppRelayer)
            .createQueryBuilder('relayer')
            .innerJoinAndSelect('relayer.app', 'app', 'app.apiKey = :apiKey', {
              apiKey: apiKey,
            })
            .leftJoinAndSelect(
              'app.origins',
              'origins',
              'origins.type =:type',
              {
                type: type,
              },
            )
            .leftJoinAndSelect('app.smartContracts', 'smartContracts');
          break;
          case OriginType.CHATAI:
          query = this.dataSource
            .getRepository(ChatAi)
            .createQueryBuilder('chatai')
            .innerJoinAndSelect('chatai.app', 'app', 'app.apiKey = :apiKey', {
              apiKey: apiKey,
            })
            .leftJoinAndSelect(
              'app.origins',
              'origins',
              'origins.type =:type',
              {
                type: type,
              },
            )
            .leftJoinAndSelect('chatai.documents', 'documents');
          break;
        default:
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'Invalid type',
          };
      }
      // const data: Application | Bundler | Paymaster | AppRelayer | any =
      //   (await query.getOne()) as Bundler | Paymaster | AppRelayer;
      const data: any = await query.getOne();
      this.logger.verbose('apiKeyValidator fetched data', JSON.stringify(data));
      if (!data) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message:
            type === OriginType.CHATAI
              ? 'Service temporarily unavailable'
              : AppMessage.INVALID_API_KEY,
        };
      }
      if (!data.app.isActive || !data.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message:
            type === OriginType.CHATAI
              ? 'Service temporarily unavailable'
              : AppMessage.ApiNotActive,
        };
      }
      // Chain ID validation - bypass for ChatAI
      if (data.app.chainId != chainId && type !== OriginType.CHATAI) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ChainNotAllowed(chainId),
        };
      }
      const allowedOrigin = data.app.origins as [];
      //origin validation - bypass for ChatAI
      if (allowedOrigin.length && type !== OriginType.CHATAI) {
        const validateOrigin = validateUrlFromDatabase(origin, allowedOrigin);

        if (!validateOrigin) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.OriginNotWhitelisted(origin, type),
          };
        }
      }
      // if (type == OriginType.PAYMASTER) {
      //   const callData = payload.params[0].callData;
      //   // const whitelistResult: any = await whitelistValidator(callData);
      //   const whitelistResult: any = await whitelistValidatorV2(
      //     callData,
      //     data.app,
      //   );
      //   if (whitelistResult.error) {
      //     return whitelistResult;
      //   }
      // }

      if (type == OriginType.PAYMASTER) {
        const entryPoint =
          payload.params?.[1] && payload.params?.[1].toLowerCase();
        let epVersion: EPVersions;

        if (SUPPORTED_ENTRYPOINTS.EPV_06 === entryPoint)
          epVersion = EPVersions.EPV_06;
        else if (SUPPORTED_ENTRYPOINTS.EPV_07 === entryPoint)
          epVersion = EPVersions.EPV_07;
        // else if (SUPPORTED_ENTRYPOINTS.EPV_08 === entryPoint)
        //   epVersion = EPVersions.EPV_08;
        else
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: `Unsupported entry point: ${entryPoint}. Supported entry points are: ${Object.values(SUPPORTED_ENTRYPOINTS).join(', ')}`,
          };

        if (epVersion === EPVersions.EPV_06 && !data.isV6Enabled) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'Entry Point V6 is not enabled for this paymaster',
          };
        }

        if (epVersion === EPVersions.EPV_07 && !data.isV7Enabled) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'Entry Point V7 is not enabled for this paymaster',
          };
        }

        // Uncomment when V8 support is added
        // if (epVersion === EPVersions.EPV_08 && !data.isV8Enabled) {
        //   return {
        //     error: true,
        //     statusCode: HttpStatus.BAD_REQUEST,
        //     message: 'Entry Point V8 is not enabled for this paymaster',
        //   };
        // }
      }
      let checkBalance;
      let args: any;
      let abi: any;
      if (type == OriginType.RELAYER) {
        const smartContracts = data?.app?.smartContracts || [];

        const activeContracts = smartContracts.filter((sc) => sc?.isActive);

        let whitelistResult;

        if (activeContracts.length) {
          whitelistResult = await whitelistValidatorForRelayerV2(
            payload,
            data.app,
          );

          if (whitelistResult.error) {
            return whitelistResult;
          }

          checkBalance = await handleValidateUserBalance(
            data,
            payload,
            whitelistResult.data,
          );
        } else {
          whitelistResult = await whitelistValidatorForRelayerV3(payload);

          if (whitelistResult.error) {
            return whitelistResult;
          }

          checkBalance = await handleValidateUserBalanceV2(
            data,
            payload,
            whitelistResult.data,
          );
        }

        if (checkBalance.error) {
          return checkBalance;
        }

        // Clone the array before modifying
        const result = [...whitelistResult.data];

        if (Array.isArray(result) && result.length > 0) {
          result[result.length - 1] = result[result.length - 1].toString();
        }

        args = result;

        abi = whitelistResult.abi;
      }

       // ChatAI credit validation logic
       if (type == OriginType.CHATAI) {
        const chatAi = data;
        const creditsRemaining = chatAi.credits;
        const subscriptionStatus = chatAi.subscriptionStatus;

        // Pro and enterprise users have unlimited credits
        if (
          subscriptionStatus === 'pro' ||
          subscriptionStatus === 'enterprise'
        ) {
          // No credit validation needed for paid plans
        } else {
          // Free users need credits for actions
          if (creditsRemaining < 1) {
            return {
              error: true,
              statusCode: HttpStatus.FORBIDDEN,
              message: `Insufficient credits for ChatAI usage. You have ${creditsRemaining} credits remaining. Upgrade to Pro for unlimited usage!`,
            };
          }
        }

        // Filter documents to only include ready ones with valid indexId and create optimized document list
        const optimizedDocuments = (chatAi.documents || [])
          .filter(
            (doc) =>
              doc.status === 'ready' &&
              doc.indexId &&
              doc.indexId.trim() !== '' &&
              doc.parsedData &&
              (doc.parsedData.text ||
                typeof doc.parsedData === 'string' ||
                (doc.parsedData.chunks &&
                  Array.isArray(doc.parsedData.chunks) &&
                  doc.parsedData.chunks.length > 0)),
          )
          .map((doc) => ({
            id: doc.id,
            filename: doc.filename,
            status: doc.status,
            indexId: doc.indexId,
            parsedData: doc.parsedData, // Include parsedData for fallback functionality
          }));

        // Create optimized response with only essential data
        return {
          error: false,
          statusCode: HttpStatus.OK,
          message: AppMessage.ServiceFetched(type),
          result: {
            id: chatAi.id,
            name: chatAi.name,
            isActive: chatAi.isActive,
            credits: chatAi.credits,
            subscriptionStatus: chatAi.subscriptionStatus,
            appId: chatAi.appId,
            app: {
              id: chatAi.app.id,
              appName: chatAi.app.appName,
              isActive: chatAi.app.isActive,
              apiKey: chatAi.app.apiKey,
              origins: chatAi.app.origins,
            },
            documents: optimizedDocuments,
            creditInfo: {
              creditsRemaining,
              subscriptionStatus,
              canUseService:
                subscriptionStatus === 'pro' ||
                subscriptionStatus === 'enterprise' ||
                creditsRemaining >= 1,
            },
          },
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ServiceFetched(type),
        result:
          type == OriginType.RELAYER
            ? {
                ...data,
                abi: abi,
                estimateGas: checkBalance?.estimateGas.toString(),
                gasPrice: checkBalance?.gasPrice,
                arguments: args.map((arg) =>
                  typeof arg === 'bigint' ? arg.toString() : arg,
                ),
              }
            : data,
      };
    } catch (error) {
      console.log('error', error);
      this.logger.error(
        'Error in catch at apiKeyValidator',
        JSON.stringify(error),
      );

      // Check if it's an RPC error (503 or similar)
      if (error?.info?.responseStatus?.includes('503')) {
        return {
          error: true,
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
          message:
            'RPC node is temporarily unavailable. Please try again later.',
        };
      }

      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }
}
